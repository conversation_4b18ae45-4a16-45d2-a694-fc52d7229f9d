import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Skeleton,
  Tooltip,
  TablePagination,
  useTheme,
  useMediaQuery,
  Fab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  DragHandle as DragHandleIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { format, parseISO } from 'date-fns';
import { WatchListService } from '../services/api/watchListService';
import { InstrumentService } from '../services/api/instrumentService';
import {
  WatchListItem,
  CreateWatchListRequest,
  UpdateWatchListRequest,
  Instrument,
  ApiResponse,
} from '../types/api';

const WatchList: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State management
  const [watchListItems, setWatchListItems] = useState<WatchListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);

  // Dialog states
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<WatchListItem | null>(null);

  // Form states
  const [formData, setFormData] = useState({
    symbol: '',
    startDate: format(new Date(), 'yyyy-MM-dd'),
    remarks: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  // Available instruments for symbol selection
  const [availableInstruments, setAvailableInstruments] = useState<Instrument[]>([]);
  const [instrumentsLoading, setInstrumentsLoading] = useState(false);

  // Load watch list items
  const loadWatchListItems = useCallback(async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const response = await WatchListService.getWatchListItems();

      if (response.success && response.data) {
        // Sort by display index
        const sortedItems = response.data.sort((a, b) => a.displayIndex - b.displayIndex);
        setWatchListItems(sortedItems);
      } else {
        setError(response.message || 'Failed to load watch list items');
      }
    } catch (err: any) {
      console.error('Error loading watch list items:', err);
      setError(err.message || 'Failed to load watch list items');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // Load available instruments for symbol selection
  const loadAvailableInstruments = useCallback(async () => {
    try {
      setInstrumentsLoading(true);
      const response = await InstrumentService.getInstruments(0, 1000, 'marketCap', 'desc');

      if (response.success && response.data) {
        setAvailableInstruments(response.data.content);
      }
    } catch (err) {
      console.error('Error loading instruments:', err);
    } finally {
      setInstrumentsLoading(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadWatchListItems();
    loadAvailableInstruments();
  }, [loadWatchListItems, loadAvailableInstruments]);

  // Form validation
  const validateForm = (isEdit = false) => {
    const errors: Record<string, string> = {};

    if (!isEdit && !formData.symbol.trim()) {
      errors.symbol = 'Symbol is required';
    }

    if (!formData.startDate) {
      errors.startDate = 'Start date is required';
    } else {
      const startDate = new Date(formData.startDate);
      const today = new Date();
      if (startDate > today) {
        errors.startDate = 'Start date cannot be in the future';
      }
    }

    if (formData.remarks && formData.remarks.length > 128) {
      errors.remarks = 'Remarks cannot exceed 128 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      symbol: '',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      remarks: '',
    });
    setFormErrors({});
    setSelectedItem(null);
  };

  // Handle add symbol
  const handleAddSymbol = async () => {
    if (!validateForm()) return;

    try {
      setSubmitting(true);

      // Calculate next display index
      const nextDisplayIndex = watchListItems.length > 0
        ? Math.max(...watchListItems.map(item => item.displayIndex)) + 1
        : 1;

      const request: CreateWatchListRequest = {
        displayIndex: nextDisplayIndex,
        symbol: formData.symbol.toUpperCase(),
        startDate: formData.startDate,
        remarks: formData.remarks.trim() || undefined,
      };

      const response = await WatchListService.createWatchListItem(request);

      if (response.success) {
        await loadWatchListItems();
        setAddDialogOpen(false);
        resetForm();
      } else {
        setError(response.message || 'Failed to add symbol to watch list');
      }
    } catch (err: any) {
      console.error('Error adding symbol:', err);
      setError(err.message || 'Failed to add symbol to watch list');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle edit symbol
  const handleEditSymbol = async () => {
    if (!selectedItem || !validateForm(true)) return;

    try {
      setSubmitting(true);

      const request: UpdateWatchListRequest = {
        remarks: formData.remarks.trim() || undefined,
      };

      const response = await WatchListService.updateWatchListItem(selectedItem.id, request);

      if (response.success) {
        await loadWatchListItems();
        setEditDialogOpen(false);
        resetForm();
      } else {
        setError(response.message || 'Failed to update watch list item');
      }
    } catch (err: any) {
      console.error('Error updating symbol:', err);
      setError(err.message || 'Failed to update watch list item');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle delete symbol
  const handleDeleteSymbol = async () => {
    if (!selectedItem) return;

    try {
      setSubmitting(true);

      const response = await WatchListService.deleteWatchListItem(selectedItem.id);

      if (response.success) {
        await loadWatchListItems();
        setDeleteDialogOpen(false);
        resetForm();
      } else {
        setError(response.message || 'Failed to delete watch list item');
      }
    } catch (err: any) {
      console.error('Error deleting symbol:', err);
      setError(err.message || 'Failed to delete watch list item');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" gutterBottom>
          Watch List
        </Typography>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => loadWatchListItems(true)}
            disabled={loading || refreshing}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setAddDialogOpen(true)}
            disabled={loading}
          >
            Add Symbol
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Watch List Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell><strong>Order</strong></TableCell>
                <TableCell><strong>Symbol</strong></TableCell>
                <TableCell><strong>Start Date</strong></TableCell>
                <TableCell><strong>Remarks</strong></TableCell>
                {!isMobile && (
                  <>
                    <TableCell align="right"><strong>1M Perf</strong></TableCell>
                    <TableCell align="right"><strong>3M Perf</strong></TableCell>
                    <TableCell align="right"><strong>6M Perf</strong></TableCell>
                  </>
                )}
                <TableCell align="center"><strong>Actions</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                // Loading skeleton
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell><Skeleton width={40} /></TableCell>
                    <TableCell><Skeleton width={80} /></TableCell>
                    <TableCell><Skeleton width={100} /></TableCell>
                    <TableCell><Skeleton width={150} /></TableCell>
                    {!isMobile && (
                      <>
                        <TableCell><Skeleton width={60} /></TableCell>
                        <TableCell><Skeleton width={60} /></TableCell>
                        <TableCell><Skeleton width={60} /></TableCell>
                      </>
                    )}
                    <TableCell><Skeleton width={100} /></TableCell>
                  </TableRow>
                ))
              ) : watchListItems.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={isMobile ? 5 : 8} align="center">
                    <Box py={4}>
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        No symbols in watch list
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Add symbols to track their performance and manage your investment watchlist.
                      </Typography>
                      <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={() => setAddDialogOpen(true)}
                      >
                        Add First Symbol
                      </Button>
                    </Box>
                  </TableCell>
                </TableRow>
              ) : (
                watchListItems
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((item) => (
                    <TableRow key={item.id} hover>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <DragHandleIcon color="disabled" sx={{ mr: 1 }} />
                          {item.displayIndex}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {item.symbol}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {format(parseISO(item.startDate), 'MMM dd, yyyy')}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                          {item.remarks || '-'}
                        </Typography>
                      </TableCell>
                      {!isMobile && (
                        <>
                          <TableCell align="right">
                            {item.oneMonthPerf ? (
                              <Chip
                                size="small"
                                label={`${(item.oneMonthPerf * 100).toFixed(2)}%`}
                                color={item.oneMonthPerf >= 0 ? 'success' : 'error'}
                                icon={item.oneMonthPerf >= 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
                              />
                            ) : (
                              '-'
                            )}
                          </TableCell>
                          <TableCell align="right">
                            {item.threeMonthPerf ? (
                              <Chip
                                size="small"
                                label={`${(item.threeMonthPerf * 100).toFixed(2)}%`}
                                color={item.threeMonthPerf >= 0 ? 'success' : 'error'}
                                icon={item.threeMonthPerf >= 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
                              />
                            ) : (
                              '-'
                            )}
                          </TableCell>
                          <TableCell align="right">
                            {item.sixMonthPerf ? (
                              <Chip
                                size="small"
                                label={`${(item.sixMonthPerf * 100).toFixed(2)}%`}
                                color={item.sixMonthPerf >= 0 ? 'success' : 'error'}
                                icon={item.sixMonthPerf >= 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
                              />
                            ) : (
                              '-'
                            )}
                          </TableCell>
                        </>
                      )}
                      <TableCell align="center">
                        <Box display="flex" gap={0.5}>
                          <Tooltip title="Edit">
                            <IconButton
                              size="small"
                              onClick={() => {
                                setSelectedItem(item);
                                setFormData({
                                  symbol: item.symbol,
                                  startDate: item.startDate,
                                  remarks: item.remarks || '',
                                });
                                setEditDialogOpen(true);
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => {
                                setSelectedItem(item);
                                setDeleteDialogOpen(true);
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {watchListItems.length > 0 && (
          <TablePagination
            rowsPerPageOptions={[10, 25, 50, 100]}
            component="div"
            count={watchListItems.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(event) => {
              setRowsPerPage(parseInt(event.target.value, 10));
              setPage(0);
            }}
            labelRowsPerPage="Items per page:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} of ${count !== -1 ? count : `more than ${to}`}`
            }
          />
        )}
      </Paper>

      {/* Add Dialog */}
      <Dialog open={addDialogOpen} onClose={() => setAddDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Symbol to Watch List</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} pt={1}>
            <FormControl fullWidth error={!!formErrors.symbol}>
              <InputLabel>Symbol</InputLabel>
              <Select
                value={formData.symbol}
                label="Symbol"
                onChange={(e) => {
                  setFormData({ ...formData, symbol: e.target.value });
                  setFormErrors({ ...formErrors, symbol: '' });
                }}
                disabled={instrumentsLoading}
              >
                {availableInstruments.map((instrument) => (
                  <MenuItem key={instrument.symbol} value={instrument.symbol}>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {instrument.symbol}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {instrument.name}
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
              {formErrors.symbol && (
                <Typography variant="caption" color="error">
                  {formErrors.symbol}
                </Typography>
              )}
            </FormControl>

            <TextField
              label="Start Date"
              type="date"
              value={formData.startDate}
              onChange={(e) => {
                setFormData({ ...formData, startDate: e.target.value });
                setFormErrors({ ...formErrors, startDate: '' });
              }}
              InputLabelProps={{ shrink: true }}
              error={!!formErrors.startDate}
              helperText={formErrors.startDate}
              fullWidth
            />

            <TextField
              label="Remarks (Optional)"
              multiline
              rows={3}
              value={formData.remarks}
              onChange={(e) => {
                setFormData({ ...formData, remarks: e.target.value });
                setFormErrors({ ...formErrors, remarks: '' });
              }}
              error={!!formErrors.remarks}
              helperText={formErrors.remarks || 'Max 128 characters'}
              inputProps={{ maxLength: 128 }}
              fullWidth
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={handleAddSymbol}
            variant="contained"
            disabled={submitting}
          >
            {submitting ? 'Adding...' : 'Add Symbol'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Watch List Item</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} pt={1}>
            <TextField
              label="Symbol"
              value={formData.symbol}
              disabled
              fullWidth
            />

            <TextField
              label="Start Date"
              type="date"
              value={formData.startDate}
              onChange={(e) => {
                setFormData({ ...formData, startDate: e.target.value });
                setFormErrors({ ...formErrors, startDate: '' });
              }}
              InputLabelProps={{ shrink: true }}
              error={!!formErrors.startDate}
              helperText={formErrors.startDate}
              fullWidth
            />

            <TextField
              label="Remarks (Optional)"
              multiline
              rows={3}
              value={formData.remarks}
              onChange={(e) => {
                setFormData({ ...formData, remarks: e.target.value });
                setFormErrors({ ...formErrors, remarks: '' });
              }}
              error={!!formErrors.remarks}
              helperText={formErrors.remarks || 'Max 128 characters'}
              inputProps={{ maxLength: 128 }}
              fullWidth
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={handleEditSymbol}
            variant="contained"
            disabled={submitting}
          >
            {submitting ? 'Updating...' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Watch List Item</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to remove <strong>{selectedItem?.symbol}</strong> from your watch list?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteSymbol}
            color="error"
            variant="contained"
            disabled={submitting}
          >
            {submitting ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WatchList;
